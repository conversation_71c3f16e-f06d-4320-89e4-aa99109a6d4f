# S3 Configuration for Domain Join Target OU

## Overview

The SSM automation document now supports reading the target OU configuration from an S3 bucket based on Business Unit (BU) prefix and Environment, instead of relying solely on the default OU stored in AWS Secrets Manager.

Configuration files are automatically selected using the naming convention: `{BUPrefix}_{Environment}.json`

## Configuration

### S3 JSON Configuration Format

Configuration files follow the naming convention: `{BUPrefix}_{Environment}.json`

Examples:
- `STM_DEV.json` - STM Business Unit, Development environment
- `STM_PRD.json` - STM Business Unit, Production environment
- `SLM_DEV.json` - SLM Business Unit, Development environment
- `SLM_PRD.json` - SLM Business Unit, Production environment

Each JSON file should have the following structure:

```json
{
  "businessName": "SanlamLife",
  "business_unit": "SPF",
  "domain": "mud.internal.co.za",
  "basePath": "OU=Servers,OU=SanlamLife,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za",
  "serverOUs": {
    "MSSQL-2022": "OU=SQL Server,OU=Server 2022,OU=Windows Server,OU=Servers,OU=SanlamLife,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za",
    "Shared-2022": "OU=Server 2022,OU=Windows Server,OU=Servers,OU=SanlamLife,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za"
  },
  "ad_domain": "MUD",
  "environment": "PRD",
  "client_tag": "SPF"
}
```

**Key Fields:**
- `basePath`: Default OU path used when no specific server type is specified
- `serverOUs`: Object containing server-type-specific OU paths
- `businessName`: Human-readable business name
- `business_unit`: Business unit identifier
- `domain`: Domain name
- `ad_domain`: Active Directory domain identifier
- `environment`: Environment identifier (e.g., "DEV", "PRD")
- `client_tag`: Client/project tag

### Usage

When executing the SSM automation document, provide the following parameters:

- `ConfigS3Bucket`: The name of the S3 bucket containing the configuration files
- `BUPrefix`: The Business Unit prefix (e.g., "STM", "SLM")
- `Environment`: The environment identifier (e.g., "DEV", "PRD")
- `ServerType`: (Optional) The server type for specific OU selection (e.g., "MSSQL-2022", "Shared-2022")

The automation will automatically construct the S3 key as: `{BUPrefix}_{Environment}.json`

**OU Selection Logic:**
1. If `ServerType` is specified and exists in `serverOUs`, use that specific OU
2. If `ServerType` is not found or not specified, fall back to `basePath`
3. If neither exists, fall back to legacy `targetOU` field
4. If S3 config fails, fall back to Secrets Manager default

Examples:
```bash
# SQL Server in STM Development environment
aws ssm start-automation-execution \
  --document-name "YourDomainJoinDocument" \
  --parameters \
    "InstanceId=i-1234567890abcdef0" \
    "DomainJoinActivity=Join" \
    "ConfigS3Bucket=my-domain-config-bucket" \
    "BUPrefix=STM" \
    "Environment=DEV" \
    "ServerType=MSSQL-2022"

# Generic server using basePath in SLM Production environment
aws ssm start-automation-execution \
  --document-name "YourDomainJoinDocument" \
  --parameters \
    "InstanceId=i-1234567890abcdef0" \
    "DomainJoinActivity=Join" \
    "ConfigS3Bucket=my-domain-config-bucket" \
    "BUPrefix=SLM" \
    "Environment=PRD"
```

### Fallback Behavior

The automation uses a hierarchical fallback approach:

1. **S3 Config with ServerType**: Uses specific OU from `serverOUs[ServerType]`
2. **S3 Config with basePath**: Uses general OU from `basePath`
3. **S3 Config with legacy targetOU**: Uses `targetOU` field (backward compatibility)
4. **Secrets Manager**: Falls back to `defaultTargetOU` from AWS Secrets Manager

If S3 configuration parameters are not fully provided (missing bucket, BU prefix, or environment) or if reading from S3 fails, the automation will fall back to using the `defaultTargetOU` from AWS Secrets Manager.

### File Organization in S3

Recommended S3 bucket structure:
```
my-domain-config-bucket/
├── STM_DEV.json
├── STM_PRD.json
├── SLM_DEV.json
├── SLM_PRD.json
└── [other BU/Environment combinations]
```

### Server Types

Common server types that can be defined in the `serverOUs` object:
- `MSSQL-2022`: SQL Server 2022 instances
- `Shared-2022`: General Windows Server 2022 instances
- `IIS-2022`: IIS Web Server instances
- `DC-2022`: Domain Controller instances
- `Exchange-2022`: Exchange Server instances

## IAM Permissions

Ensure the SSM automation execution role has the following S3 permissions:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject"
      ],
      "Resource": "arn:aws:s3:::your-config-bucket/*"
    }
  ]
}
```

## Benefits

1. **Centralized Configuration**: Store domain join configurations in S3 for easy management
2. **Business Unit & Environment Separation**: Different OUs for different BU/Environment combinations
3. **Automatic File Selection**: No need to specify full S3 paths - just BU and Environment
4. **Standardized Naming**: Consistent `{BUPrefix}_{Environment}.json` naming convention
5. **Version Control**: Track changes to configurations using S3 versioning
6. **Audit Trail**: Monitor configuration access through CloudTrail
7. **Flexibility**: Switch between different OUs without modifying the automation document
8. **Scalability**: Easy to add new BU/Environment combinations by adding new JSON files
