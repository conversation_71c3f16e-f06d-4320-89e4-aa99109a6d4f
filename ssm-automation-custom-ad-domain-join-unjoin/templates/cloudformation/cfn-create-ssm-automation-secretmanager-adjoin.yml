AWSTemplateFormatVersion: '2010-09-09'
Description: Create AD credentials, required for the AD domain join and unjoin activities, which are stored as a secret in AWS Secrets Manager and an AWS Systems Manager SM Automation runbook which automates the process.
Transform: AWS::LanguageExtensions
Parameters:
  SSMAutomationRunbookNameParam:
    Description: 'Automation runbook that will join or unjoin an EC2 Windows instance to Active Directory (AD). The name must be between 3 and 128 characters. Valid characters are a-z, A-Z, 0-9, and _, -, and . only. You can''t use the following strings as document name prefixes. These are reserved by AWS for use as document name prefixes: aws-,amazon, amzn.'
    Type: String
    AllowedPattern: ^[a-zA-Z0-9_\-.]{3,128}$
  ADDomainNameParam:
    Description: The FQDN of the AD domain.
    Type: String
    Default: corp.example.com
    AllowedPattern: ^([a-zA-Z0-9]+[\.-])+([a-zA-Z0-9])+$
  ADUserNameParam:
    Description: The AD username that has delegated rights to perform domain join activities. Enter the name in down-level logon name format, DOMAIN\Username.
    Type: String
    Default: CORP\Admin
    AllowedPattern: '[A-Z]+\\[\w.-]+'
  ADPasswordParam:
    Description: AD domain join password.
    Type: String
    NoEcho: true
    AllowedPattern: (?=^.{6,255}$)((?=.*\d)(?=.*[A-Z])(?=.*[a-z])|(?=.*\d)(?=.*[^A-Za-z0-9])(?=.*[a-z])|(?=.*[^A-Za-z0-9])(?=.*[A-Z])(?=.*[a-z])|(?=.*\d)(?=.*[A-Z])(?=.*[^A-Za-z0-9]))^.*
    MaxLength: '64'
    MinLength: '8'
  ADTargetOUParam:
    Description: Enter the Organizational Unit (OU) where your domain joined server will reside.
    Type: String
    Default: OU=Computers,OU=CORP,dc=corp,dc=example,dc=com
Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
      - Label:
          default: AWS Systems Manager Automation runbook and AWS Secrets Manager secret creation
        Parameters:
          - SSMAutomationRunbookNameParam
          - ADDomainNameParam
          - ADUserNameParam
          - ADPasswordParam
          - ADTargetOUParam
    ParameterLabels:
      SSMAutomationRunbookNameParam:
        default: Automation runbook name
      ADDomainNameParam:
        default: AD domain (FQDN)
      ADUserNameParam:
        default: AD domain user
      ADPasswordParam:
        default: AD domain user's password
      ADTargetOUParam:
        default: Target Organizational Unit (OU)
Resources:
  KMSKeyResource:
    Type: AWS::KMS::Key
    Properties:
      MultiRegion: true
      KeyPolicy:
        Version: 2012-10-17
        Id: key-ad-credentials
        Statement:
          - Sid: Enable IAM User Permissions
            Effect: Allow
            Principal:
              AWS: !Sub 'arn:${AWS::Partition}:iam::${AWS::AccountId}:root'
            Action: 'kms:*'
            Resource: '*'
  SecretKeyADPasswordResource:
    Type: AWS::SecretsManager::Secret
    DependsOn: KMSKeyResource
    Properties:
      SecretString:
        Fn::ToJsonString:
          domainName: !Ref ADDomainNameParam
          domainJoinUserName: !Ref ADUserNameParam
          domainJoinPassword: !Ref ADPasswordParam
          defaultTargetOU: !Ref ADTargetOUParam
      KmsKeyId: !Ref KMSKeyResource
  ADEC2IAMRoleResource:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - ec2.amazonaws.com
            Action:
              - sts:AssumeRole
      Path: /
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore
      Description: IAM Instance Profile for EC2 instances or Launch templates allowing AWS Systems Manager, AWS Secrets Manager, and AWS Key Management Service access.
      Policies:
        - PolicyName: AD-Demo-SSM-KMS-SEC
          PolicyDocument:
            Version: 2012-10-17
            Statement:
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                  - kms:DescribeKey
                  - kms:Decrypt
                Resource:
                  - !Ref SecretKeyADPasswordResource
                  - !GetAtt KMSKeyResource.Arn
  ADEC2IAMInstanceProfileResource:
    Type: AWS::IAM::InstanceProfile
    Properties:
      Path: /
      Roles:
        - !Ref ADEC2IAMRoleResource
  SSMAutomationRunbookResource:
    Type: AWS::SSM::Document
    Properties:
      DocumentType: Automation
      DocumentFormat: YAML
      Name: !Ref 'SSMAutomationRunbookNameParam'
      Content:
        !Sub |
          schemaVersion: '0.3'
          description: |-
            This document will join or unjoin an EC2 Windows instance to an Active Directory domain.
          assumeRole: '{{AutomationAssumeRole}}'
          parameters:
            AutomationAssumeRole:
              default: ''
              description: (Optional) The ARN of the role that allows Automation to perform the actions on your behalf.
              type: String
            InstanceId:
              description: (Required) The Instance running Windows Server.
              type: String
            DomainJoinActivity:
              allowedValues:
                - Join
                - Unjoin
                - ''
              default: ''
              description: '(Required) Select which AD domain activity to perform, join an AD domain or unjoin an AD domain.'
              type: String
          mainSteps:
            - name: assertInstanceIsWindows
              action: 'aws:assertAwsResourceProperty'
              description: Verify the OS of the EC2 instance. Specifically, if it is Windows Server then it the runbook will continue.
              inputs:
                Service: ec2
                PropertySelector: '$.Reservations[0].Instances[0].Platform'
                Api: DescribeInstances
                DesiredValues:
                  - windows
                InstanceIds:
                  - '{{InstanceId}}'
              timeoutSeconds: 10
              nextStep: chooseDomainJoinActivity
            - name: chooseDomainJoinActivity
              action: aws:branch
              timeoutSeconds: 60
              description: Determine the appropriate AD domain activity, join or unjoin.
              inputs:
                Choices:
                  - NextStep: joinDomain
                    StringEquals: Join
                    Variable: '{{DomainJoinActivity}}'
                  - NextStep: unjoinDomain
                    StringEquals: Unjoin
                    Variable: '{{DomainJoinActivity}}'
              isCritical: 'true'
              isEnd: false
            - name: joinDomain
              action: aws:runCommand
              description: Execute PowerShell locally on EC2 instance to join the AD domain.
              inputs:
                Parameters:
                  commands: |-
                    If ((Get-CimInstance -ClassName 'Win32_ComputerSystem' -ErrorAction SilentlyContinue | Select-Object -ExpandProperty 'PartOfDomain') -eq $false) {
                        Try {
                            $jsonSecretValue = (Get-SECSecretValue -SecretId ${SecretKeyADPasswordResource}).SecretString | ConvertFrom-Json
                            $targetOU = $jsonSecretValue.defaultTargetOU
                            $domainName = $jsonSecretValue.domainName
                            $domainJoinUserName = $jsonSecretValue.domainJoinUserName
                            $domainJoinPassword = $jsonSecretValue.domainJoinPassword | ConvertTo-SecureString -AsPlainText -Force
                        } Catch [System.Exception] {
                            Write-Output "Failed to get secret $_"
                        }
                        $domainCredential = New-Object System.Management.Automation.PSCredential($domainJoinUserName, $domainJoinPassword)

                        Try {
                            Write-Output "Attempting to join $env:COMPUTERNAME to Active Directory domain: $domainName and moving $env:COMPUTERNAME to the following OU: $targetOU."
                            Add-Computer -ComputerName $env:COMPUTERNAME -DomainName $domainName -Credential $domainCredential -OUPath $targetOU -Restart:$false -ErrorAction Stop 
                        } Catch [System.Exception] {
                            Write-Output "Failed to add computer to the domain $_"
                            Exit 1
                        }
                    } Else {
                        Write-Output "$env:COMPUTERNAME is already part of the Active Directory domain $domainName."
                        Exit 0
                    }
                InstanceIds:
                  - '{{InstanceId}}'
                DocumentName: AWS-RunPowerShellScript
              timeoutSeconds: 600
              nextStep: joinADEC2Tag
              isEnd: false
              onFailure: step:failADEC2Tag
            - name: joinADEC2Tag
              action: aws:createTags
              description: Add the ADJoined EC2 tag to reflect joining to AD domain.
              inputs:
                ResourceIds:
                  - '{{InstanceId}}'
                ResourceType: EC2
                Tags:
                  - Value: Join-complete
                    Key: ADJoined
              isEnd: false
              nextStep: rebootServer
            - name: unjoinDomain
              action: aws:runCommand
              description: Execute PowerShell locally on EC2 instance to unjoin from the AD domain.
              inputs:
                Parameters:
                  commands: |-
                    If ((Get-CimInstance -ClassName 'Win32_ComputerSystem' -ErrorAction SilentlyContinue | Select-Object -ExpandProperty 'PartOfDomain') -eq $true) {
                        Try {
                            $jsonSecretValue = (Get-SECSecretValue -SecretId ${SecretKeyADPasswordResource}).SecretString | ConvertFrom-Json
                            $domainName = $jsonSecretValue.domainName
                            $domainJoinUserName = $jsonSecretValue.domainJoinUserName
                            $domainJoinPassword = $jsonSecretValue.domainJoinPassword | ConvertTo-SecureString -AsPlainText -Force
                        } Catch [System.Exception] {
                            Write-Output "Failed to get secret $_"
                        }

                        $domainCredential = New-Object System.Management.Automation.PSCredential($domainJoinUserName, $domainJoinPassword)

                        If (-not (Get-WindowsFeature -Name 'RSAT-AD-Tools' -ErrorAction SilentlyContinue | Select-Object -ExpandProperty 'Installed')) {
                            Write-Output 'Installing RSAT AD Tools to allow domain joining'
                            Try {
                                $Null = Add-WindowsFeature -Name 'RSAT-AD-Tools' -ErrorAction Stop
                            } Catch [System.Exception] {
                                Write-Output "Failed to install RSAT AD Tools $_"
                                Exit 1
                            }    
                        }
                        
                        $getADComputer = (Get-ADComputer -Identity $env:COMPUTERNAME -Credential $domainCredential)
                        $distinguishedName = $getADComputer.DistinguishedName

                        Try {
                            Remove-Computer -ComputerName $env:COMPUTERNAME -UnjoinDomainCredential $domainCredential -Verbose -Force -Restart:$false -ErrorAction Stop
                            Remove-ADComputer -Credential $domainCredential -Identity $distinguishedName -Server $domainName -Confirm:$False -Verbose -ErrorAction Stop
                        } Catch [System.Exception] {
                            Write-Output "Failed to remove $env:COMPUTERNAME from the $domainName domain and in a Windows Workgroup. $_"
                            Exit 1
                        }  
                    } Else {
                        Write-Output "$env:COMPUTERNAME is not part of the Active Directory domain $domainName and already part of a Windows Workgroup."
                        Exit 0
                    }
                InstanceIds:
                  - '{{InstanceId}}'
                DocumentName: AWS-RunPowerShellScript
              timeoutSeconds: 600
              nextStep: unjoinADEC2Tag
              isEnd: false
              onFailure: step:failADEC2Tag
            - name: unjoinADEC2Tag
              action: aws:createTags
              description: Update the ADJoined EC2 tag to reflect removal from AD domain.
              inputs:
                ResourceIds:
                  - '{{InstanceId}}'
                ResourceType: EC2
                Tags:
                  - Value: Unjoin-complete
                    Key: ADJoined
              timeoutSeconds: 30
              isEnd: false
              nextStep: stopServer
            - name: failADEC2Tag
              action: aws:createTags
              description: Update the ADJoined EC2 tag to reflect a failure in the AD domain join/unjoin process.
              inputs:
                ResourceIds:
                  - '{{InstanceId}}'
                ResourceType: EC2
                Tags:
                  - Value: Failed
                    Key: ADJoined
              timeoutSeconds: 30
              isEnd: false
              nextStep: stopServer
            - name: rebootServer
              action: aws:executeAwsApi
              inputs:
                Service: ec2
                Api: RebootInstances
                InstanceIds:
                  - '{{InstanceId}}'
              isEnd: true
            - name: stopServer
              action: 'aws:executeAwsApi'
              inputs:
                Service: ec2
                Api: StopInstances
                InstanceIds:
                  - '{{InstanceId}}'
              isEnd: true
Outputs:
  SSMAutomationARN:
    Description: 'The full ARN of the Automation runbook.'
    Value: !Sub 'arn:${AWS::Partition}:ssm:${AWS::Region}:${AWS::AccountId}:automation-definition/${SSMAutomationRunbookResource}'
  ADEC2IAMInstanceProfileARN:
    Description: 'The full ARN of the IAM Instance Profile.'
    Value: !GetAtt ADEC2IAMInstanceProfileResource.Arn
  KMSKeyARN:
    Description: 'The full ARN of the KMS Key.'
    Value: !GetAtt KMSKeyResource.Arn
