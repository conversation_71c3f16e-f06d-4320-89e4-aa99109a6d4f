# S3 Configuration for Domain Join Target OU

## Overview

The SSM automation document now supports reading the target OU configuration from an S3 bucket instead of relying solely on the default OU stored in AWS Secrets Manager.

## Configuration

### S3 JSON Configuration Format

Create a JSON file in S3 with the following structure:

```json
{
  "targetOU": "OU=Servers,OU=Production,DC=example,DC=com",
  "description": "Domain join configuration for production servers",
  "environment": "production",
  "lastUpdated": "2025-01-02T00:00:00Z",
  "updatedBy": "<EMAIL>"
}
```

**Required Fields:**
- `targetOU`: The distinguished name of the target Organizational Unit

**Optional Fields:**
- `description`: Human-readable description of the configuration
- `environment`: Environment identifier (e.g., "production", "staging", "dev")
- `lastUpdated`: ISO 8601 timestamp of last update
- `updatedBy`: Email or identifier of who last updated the configuration

### Usage

When executing the SSM automation document, provide the following parameters:

- `ConfigS3Bucket`: The name of the S3 bucket containing the configuration file
- `ConfigS3Key`: The S3 key (path) to the JSON configuration file

Example:
```bash
aws ssm start-automation-execution \
  --document-name "YourDomainJoinDocument" \
  --parameters \
    "InstanceId=i-1234567890abcdef0" \
    "DomainJoinActivity=Join" \
    "ConfigS3Bucket=my-domain-config-bucket" \
    "ConfigS3Key=environments/production/domain-join-config.json"
```

### Fallback Behavior

If S3 configuration parameters are not provided or if reading from S3 fails, the automation will fall back to using the `defaultTargetOU` from AWS Secrets Manager.

## IAM Permissions

Ensure the SSM automation execution role has the following S3 permissions:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject"
      ],
      "Resource": "arn:aws:s3:::your-config-bucket/*"
    }
  ]
}
```

## Benefits

1. **Centralized Configuration**: Store domain join configurations in S3 for easy management
2. **Environment-Specific OUs**: Different configurations for different environments
3. **Version Control**: Track changes to configurations using S3 versioning
4. **Audit Trail**: Monitor configuration access through CloudTrail
5. **Flexibility**: Switch between different OUs without modifying the automation document
