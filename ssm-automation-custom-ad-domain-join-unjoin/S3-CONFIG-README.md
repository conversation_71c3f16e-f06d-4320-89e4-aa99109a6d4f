# S3 Configuration for Domain Join Target OU

## Overview

The SSM automation document now supports reading the target OU configuration from an S3 bucket based on Business Unit (BU) prefix and Environment, instead of relying solely on the default OU stored in AWS Secrets Manager.

Configuration files are automatically selected using the naming convention: `{BUPrefix}_{Environment}.json`

## Configuration

### S3 JSON Configuration Format

Configuration files follow the naming convention: `{BUPrefix}_{Environment}.json`

Examples:
- `STM_DEV.json` - STM Business Unit, Development environment
- `STM_PRD.json` - STM Business Unit, Production environment
- `SLM_DEV.json` - SLM Business Unit, Development environment
- `SLM_PRD.json` - SLM Business Unit, Production environment

Each JSON file should have the following structure:

```json
{
  "targetOU": "OU=Servers,OU=STM,OU=Development,DC=example,DC=com",
  "description": "Domain join configuration for STM Development servers",
  "businessUnit": "STM",
  "environment": "DEV",
  "lastUpdated": "2025-01-02T00:00:00Z",
  "updatedBy": "<EMAIL>"
}
```

**Required Fields:**
- `targetOU`: The distinguished name of the target Organizational Unit

**Optional Fields:**
- `description`: Human-readable description of the configuration
- `businessUnit`: Business unit identifier (e.g., "STM", "SLM")
- `environment`: Environment identifier (e.g., "DEV", "PRD")
- `lastUpdated`: ISO 8601 timestamp of last update
- `updatedBy`: Email or identifier of who last updated the configuration

### Usage

When executing the SSM automation document, provide the following parameters:

- `ConfigS3Bucket`: The name of the S3 bucket containing the configuration files
- `BUPrefix`: The Business Unit prefix (e.g., "STM", "SLM")
- `Environment`: The environment identifier (e.g., "DEV", "PRD")

The automation will automatically construct the S3 key as: `{BUPrefix}_{Environment}.json`

Examples:
```bash
# STM Development environment
aws ssm start-automation-execution \
  --document-name "YourDomainJoinDocument" \
  --parameters \
    "InstanceId=i-1234567890abcdef0" \
    "DomainJoinActivity=Join" \
    "ConfigS3Bucket=my-domain-config-bucket" \
    "BUPrefix=STM" \
    "Environment=DEV"

# SLM Production environment
aws ssm start-automation-execution \
  --document-name "YourDomainJoinDocument" \
  --parameters \
    "InstanceId=i-1234567890abcdef0" \
    "DomainJoinActivity=Join" \
    "ConfigS3Bucket=my-domain-config-bucket" \
    "BUPrefix=SLM" \
    "Environment=PRD"
```

### Fallback Behavior

If S3 configuration parameters are not fully provided (missing bucket, BU prefix, or environment) or if reading from S3 fails, the automation will fall back to using the `defaultTargetOU` from AWS Secrets Manager.

### File Organization in S3

Recommended S3 bucket structure:
```
my-domain-config-bucket/
├── STM_DEV.json
├── STM_PRD.json
├── SLM_DEV.json
├── SLM_PRD.json
└── [other BU/Environment combinations]
```

## IAM Permissions

Ensure the SSM automation execution role has the following S3 permissions:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject"
      ],
      "Resource": "arn:aws:s3:::your-config-bucket/*"
    }
  ]
}
```

## Benefits

1. **Centralized Configuration**: Store domain join configurations in S3 for easy management
2. **Business Unit & Environment Separation**: Different OUs for different BU/Environment combinations
3. **Automatic File Selection**: No need to specify full S3 paths - just BU and Environment
4. **Standardized Naming**: Consistent `{BUPrefix}_{Environment}.json` naming convention
5. **Version Control**: Track changes to configurations using S3 versioning
6. **Audit Trail**: Monitor configuration access through CloudTrail
7. **Flexibility**: Switch between different OUs without modifying the automation document
8. **Scalability**: Easy to add new BU/Environment combinations by adding new JSON files
